"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/toast"
import UnderDevelopment from "@/components/ui/under-development"
import { api } from "@/utils/api"
import { getAuthToken } from "@/utils/auth"
import { getUserProfile, UserProfileData } from "@/utils/users-api"

type ExtendedUser = {
  id: string
  name: string
  email: string
  avatar?: string
  role: "user" | "admin"
  emailVerified?: boolean
  membership?: string
  organization?: string
  country?: string
  phone?: string
  position?: string
  paid?: boolean
}

// Abstract submission record type definition
type AbstractSubmission = {
  abstract_id: number
  user_id: number
  title: string
  publication_status: string
  oral_presentation: boolean
  poster_presentation: boolean
  abstract_path: string
  filename?: string
  submit_time: string
  theme: string
}

// Hotel accommodation data type definition
type HotelRoomData = {
  room_id: number
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  checkin_date: string
  checkout_date: string
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  Id: number
  room_type: string
}

// User booking information type definition (based on actual backend response)
type UserBooking = {
  executor_username: string
  hotel_id: number
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  checkin_date: string
  checkout_date: string
  room_id: number
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  RoomId: number
  SharedOption: number
  AssignedRoomId: number
  occupant: number
  is_assigned: boolean
  room_type: string
  shared_option: string
  username: string
  gender: string
  country: string
  organization: string
  roommate: {
    executor_username: string
    Id: number
    RoomId: number
    SharedOption: number
    AssignedRoomId: number
    occupant: number
    checkin_date: string
    checkout_date: string
    is_assigned: boolean
    username: string
    name: string
    gender: string
    country: string
    organization: string
  }
}

// 从文件名提取原始文件名
const getOriginalFileName = (filePath: string) => {
  if (!filePath) return "Unknown"

  // 从URL中提取文件名
  const urlParts = filePath.split("/")
  const fileName = urlParts[urlParts.length - 1]

  if (!fileName) return "Unknown"

  // 移除URL参数
  const cleanFileName = fileName.split("?")[0]

  if (!cleanFileName) return "Unknown"

  // URL解码文件名
  const decodedFileName = decodeURIComponent(cleanFileName)

  // 尝试提取原始文件名（移除时间戳等后缀）
  const match = decodedFileName.match(/^(.+?)_user_\d+_\d+\.(.+)$/)
  if (match) {
    return `${match[1]}.${match[2]}`
  }

  return decodedFileName
}

// 格式化日期
const formatDate = (dateString: string, options?: { includeTime?: boolean }) => {
  const date = new Date(dateString)
  if (options?.includeTime) {
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

export default function DashboardOverview() {
  const { user: contextUser } = useUser()
  const { addToast } = useToast()
  const [user, setUser] = useState<ExtendedUser | null>(null)
  const [isResending, setIsResending] = useState(false)
  const [lastSentTime, setLastSentTime] = useState<number | null>(null)
  const [remainingTime, setRemainingTime] = useState(0)
  const [daysUntilEvent, setDaysUntilEvent] = useState(0)
  const [abstractSubmissions, setAbstractSubmissions] = useState<AbstractSubmission[]>([])
  const [isLoadingSubmissions, setIsLoadingSubmissions] = useState(false)
  const [userBookings, setUserBookings] = useState<UserBooking[]>([])
  const [isLoadingBookings, setIsLoadingBookings] = useState(false)

  useEffect(() => {
    async function fetchUserData() {
      if (!contextUser) return

      try {
        // 获取认证信息
        const authData = localStorage.getItem("user")
        if (!authData) return

        const parsedAuthData = JSON.parse(authData) as Record<string, unknown>
        const authToken =
          (parsedAuthData.token as string) ||
          ((parsedAuthData.data as Record<string, unknown>) &&
            ((parsedAuthData.data as Record<string, unknown>).token as string))

        if (!authToken) return

        // 从 API 获取完整的用户数据（不再需要传递authToken，由API客户端自动处理）
        const userProfileData: UserProfileData = await getUserProfile(contextUser.id)

        // 构建扩展的用户对象
        const roleValue = userProfileData.role || "user"
        const extendedUser: ExtendedUser = {
          id: userProfileData.id.toString(),
          name: userProfileData.name,
          email: userProfileData.email,
          role: roleValue === "admin" || roleValue === "user" ? roleValue : "user",
          emailVerified: userProfileData.email_verified === true,
          membership: userProfileData.membership,
          organization: userProfileData.organization,
          country: userProfileData.country,
          phone: userProfileData.phone,
          position: userProfileData.position,
          paid: (userProfileData as UserProfileData & { paid?: boolean }).paid === true, // 从API响应中获取paid字段
        }

        setUser(extendedUser)
      } catch (error) {
        console.error("Error fetching user data:", error)
        // 如果 API 调用失败，回退到上下文中的基本用户数据
        setUser(contextUser as ExtendedUser)
      }
    }

    fetchUserData()
  }, [contextUser])

  // 获取abstract submissions
  useEffect(() => {
    async function fetchAbstractSubmissions() {
      if (!user?.id) return

      setIsLoadingSubmissions(true)
      try {
        const result = await api.get<
          | {
              code?: number
              data?: {
                abstract_id: number
                title: string
                theme: string
                publication_status: string
                oral_presentation: boolean
                poster_presentation: boolean
                abstract_path: string
                submit_time: string
                status?: string
              }
              msg?: string
            }
          | {
              abstract_id: number
              title: string
              theme: string
              publication_status: string
              oral_presentation: boolean
              poster_presentation: boolean
              abstract_path: string
              submit_time: string
              status?: string
            }
          | null
        >(`/api/abstracts/get/${user.id}`)

        let abstractData = null

        // 检查是否有标准API响应格式
        if (result && typeof result === "object" && "code" in result) {
          if (result.code === 200 && result.data) {
            abstractData = result.data
          }
        } else if (result && typeof result === "object" && "abstract_id" in result) {
          // 处理直接返回数据的情况（向后兼容）
          abstractData = result
        }

        // 严格检查：abstractData必须存在，不能是null，且必须有abstract_id和title
        if (abstractData && abstractData.abstract_id && abstractData.title) {
          // 映射API字段到前端期望的字段
          const mappedData: AbstractSubmission = {
            abstract_id: abstractData.abstract_id,
            user_id: 0, // 添加缺失的字段，使用默认值
            title: abstractData.title,
            theme: abstractData.theme,
            publication_status: abstractData.publication_status,
            oral_presentation: abstractData.oral_presentation,
            poster_presentation: abstractData.poster_presentation,
            abstract_path: abstractData.abstract_path,
            submit_time: abstractData.submit_time,
          }
          setAbstractSubmissions([mappedData])
        } else {
          // No submissions found or data is null - this is normal
          setAbstractSubmissions([])
        }
      } catch (error) {
        console.error("Error fetching abstract submissions:", error)
        // Set empty array on error to show "no submissions" state
        setAbstractSubmissions([])
      } finally {
        setIsLoadingSubmissions(false)
      }
    }

    fetchAbstractSubmissions()
  }, [user?.id])

  // 获取用户预订信息
  useEffect(() => {
    async function fetchUserBookings() {
      if (!user?.id) return

      setIsLoadingBookings(true)
      try {
        const authToken = getAuthToken()
        if (!authToken) {
          console.log("No auth token found for bookings")
          return
        }

        const result = await api.get<{
          code?: number
          data?: UserBooking
          msg?: string
        }>("/api/accommodations/me", { user_id: user.id.toString() })

        // 检查是否有标准API响应格式
        if (result && typeof result === "object" && "code" in result) {
          if (result.code === 200) {
            // 检查返回的数据是否为空预订（所有字段都是默认值）
            const booking = result.data
            const isEmptyBooking =
              !booking ||
              booking.hotel_id === 0 ||
              booking.chinese_name === "" ||
              booking.room_type === "" ||
              booking.checkin_date === "0001-01-01T00:00:00Z"

            setUserBookings(isEmptyBooking ? [] : [booking])
          } else {
            console.error("Failed to fetch user bookings:", result.msg)
            setUserBookings([])
          }
        } else {
          // 处理直接返回数据的情况（向后兼容）
          const booking = result as UserBooking
          const isEmptyBooking =
            !booking ||
            booking.hotel_id === 0 ||
            booking.chinese_name === "" ||
            booking.room_type === "" ||
            booking.checkin_date === "0001-01-01T00:00:00Z"

          setUserBookings(isEmptyBooking ? [] : [booking])
        }
      } catch (error) {
        console.error("Error fetching user bookings:", error)
        const errorMessage = error instanceof Error ? error.message : "Unknown error"
        console.error("Detailed error:", errorMessage)
        setUserBookings([])
      } finally {
        setIsLoadingBookings(false)
      }
    }

    fetchUserBookings()
  }, [user?.id])

  // 初始化重发验证邮件的状态
  useEffect(() => {
    const savedTime = localStorage.getItem("dashboard-verification-sent-time")
    if (savedTime) {
      const sentTime = parseInt(savedTime, 10)
      const elapsed = Date.now() - sentTime
      const cooldownPeriod = 120000 // 120秒

      if (elapsed < cooldownPeriod) {
        setLastSentTime(sentTime)
        setRemainingTime(Math.ceil((cooldownPeriod - elapsed) / 1000))
      } else {
        // 如果超过120秒，清除存储的时间
        localStorage.removeItem("dashboard-verification-sent-time")
      }
    }
  }, [])

  // 倒计时逻辑
  useEffect(() => {
    if (remainingTime > 0) {
      const timer = setTimeout(() => {
        setRemainingTime((prev) => {
          if (prev <= 1) {
            setLastSentTime(null)
            localStorage.removeItem("dashboard-verification-sent-time")
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [remainingTime])

  // 计算距离会议的天数
  useEffect(() => {
    const calculateDaysUntilEvent = () => {
      const eventDate = new Date("2025-10-16") // October 16, 2025
      const today = new Date()
      const timeDifference = eventDate.getTime() - today.getTime()
      const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24))

      // 如果会议已经过去，显示0
      setDaysUntilEvent(daysDifference > 0 ? daysDifference : 0)
    }

    calculateDaysUntilEvent()
    // 每天更新一次
    const interval = setInterval(calculateDaysUntilEvent, 24 * 60 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  // 重发验证邮件功能
  const handleResendVerification = async () => {
    if (isResending || remainingTime > 0 || !user?.email) return

    setIsResending(true)
    try {
      // 获取认证token
      const authToken = getAuthToken()
      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Error",
          message: "Please login again to resend verification email",
        })
        return
      }

      // 调用重发验证邮件的API
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/verify/resend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({ email: user.email }),
      })

      const result = (await response.json()) as {
        code?: number
        msg?: string
        message?: string
      }

      if (response.ok && result.code === 200) {
        // 成功发送
        const currentTime = Date.now()
        setLastSentTime(currentTime)
        setRemainingTime(120) // 120秒倒计时
        localStorage.setItem("dashboard-verification-sent-time", currentTime.toString())

        addToast({
          type: "success",
          title: "Email Sent",
          message: "Verification email has been sent. Please check your inbox and spam folder.",
        })
      } else {
        // API返回错误
        addToast({
          type: "error",
          title: "Failed to Send",
          message: result.message || result.msg || "Failed to send verification email. Please try again.",
        })
      }
    } catch (error) {
      console.error("Failed to resend verification email:", error)
      addToast({
        type: "error",
        title: "Network Error",
        message: "Failed to send verification email. Please check your connection and try again.",
      })
    } finally {
      setIsResending(false)
    }
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin mb-4 text-4xl text-gray-400"></i>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <h1 className="mb-2 text-3xl font-bold text-gray-900">Welcome back, {user.name}!</h1>
        <p className="text-gray-600">Manage your conference registration and profile</p>
      </motion.div>

      {/* Email Verification Notice */}
      {user.role !== "admin" && !user.emailVerified && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="border-border bg-muted">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <i className="fas fa-exclamation-triangle text-muted-foreground text-xl"></i>
                </div>
                <div className="flex-1">
                  <h3 className="text-foreground mb-2 text-lg font-medium">Email Verification Required</h3>
                  <p className="text-muted-foreground mb-4">
                    Your email address has not been verified yet. To access all conference features and pages, please
                    verify your email address. You can only access the Dashboard and Profile pages until verification is
                    complete.
                  </p>
                  <div className="flex flex-col gap-3 sm:flex-row">
                    <Button
                      variant="outline"
                      onClick={handleResendVerification}
                      disabled={isResending || remainingTime > 0}
                    >
                      {isResending ? (
                        <>
                          <i className="fas fa-spinner fa-spin mr-2"></i>
                          Sending...
                        </>
                      ) : remainingTime > 0 ? (
                        <>
                          <i className="fas fa-clock mr-2"></i>
                          Resend in {remainingTime}s
                        </>
                      ) : (
                        <>
                          <i className="fas fa-envelope mr-2"></i>
                          Resend Verification Email
                        </>
                      )}
                    </Button>
                    <Link href="/profile">
                      <Button variant="outline">
                        <i className="fas fa-user mr-2"></i>
                        Go to Profile
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="grid grid-cols-1 gap-6 md:grid-cols-3"
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
            <i className={`fas fa-credit-card ${user.paid ? "text-green-600" : "text-amber-600"}`}></i>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user.paid ? "Paid" : "Pending"}</div>
            <p className="text-muted-foreground text-xs">Registration fee</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submission Status</CardTitle>
            <i
              className={`fas fa-file-alt ${
                abstractSubmissions.length > 0 && abstractSubmissions[0]
                  ? "text-green-600" // 有提交就显示绿色
                  : "text-blue-600"
              }`}
            ></i>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoadingSubmissions
                ? "Loading..."
                : abstractSubmissions.length > 0 && abstractSubmissions[0]
                  ? "Submitted"
                  : "Not Submitted"}
            </div>
            <p className="text-muted-foreground text-xs">
              {abstractSubmissions.length > 0 ? "Abstract submission" : "Abstract submission"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Days Until Event</CardTitle>
            <i className="fas fa-calendar text-purple-600"></i>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{daysUntilEvent}</div>
            <p className="text-muted-foreground text-xs">
              {daysUntilEvent === 0 ? "Event has started/passed" : "IFMB 2025 - October 16"}
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="submission">Submission</TabsTrigger>
            <TabsTrigger value="accommodation">Accommodation</TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Name</label>
                      <p className="mt-1 text-sm text-gray-900">{user.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Email</label>
                      <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Role</label>
                      <p className="mt-1 text-sm text-gray-900 capitalize">{user.role}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Email Verification</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {user.emailVerified ? (
                          <span className="flex items-center text-green-600">
                            <i className="fas fa-check-circle mr-1"></i> Verified
                          </span>
                        ) : (
                          <span className="flex items-center text-amber-600">
                            <i className="fas fa-exclamation-circle mr-1"></i> Not Verified
                          </span>
                        )}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Membership Type</label>
                      <p className="mt-1 text-sm text-gray-900">{user.membership || "Not specified"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Organization</label>
                      <p className="mt-1 text-sm text-gray-900">{user.organization || "Not specified"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Position</label>
                      <p className="mt-1 text-sm text-gray-900">{user.position || "Not specified"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Country</label>
                      <p className="mt-1 text-sm text-gray-900">{user.country || "Not specified"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Phone</label>
                      <p className="mt-1 text-sm text-gray-900">{user.phone || "Not specified"}</p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Link href="/profile">
                      <Button>
                        <i className="fas fa-edit mr-2"></i>
                        Edit Profile
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Submission Tab */}
          <TabsContent value="submission" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Abstract Submission</CardTitle>
                <CardDescription>
                  {abstractSubmissions.length > 0
                    ? "Your abstract submission information"
                    : "Submit your research abstract for IFMB 2025"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Loading State */}
                  {isLoadingSubmissions && (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <i className="fas fa-spinner fa-spin mb-2 text-2xl text-gray-400"></i>
                        <p className="text-gray-600">Loading submission status...</p>
                      </div>
                    </div>
                  )}

                  {/* No Submission State */}
                  {!isLoadingSubmissions && abstractSubmissions.length === 0 && (
                    <div className="rounded-lg border border-gray-200 p-6 text-center">
                      <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                        <i className="fas fa-file-alt text-2xl text-gray-400"></i>
                      </div>
                      <h3 className="mb-2 text-lg font-medium text-gray-900">No Abstract Submitted</h3>
                      <p className="mb-4 text-gray-600">
                        You haven't submitted any abstract yet. Start your submission to participate in IFMB 2025.
                      </p>
                      <Link href="/dashboard/submission">
                        <Button>
                          <i className="fas fa-paper-plane mr-2"></i>
                          Start Submission
                        </Button>
                      </Link>
                    </div>
                  )}

                  {/* Submitted Abstract Information */}
                  {!isLoadingSubmissions && abstractSubmissions.length > 0 && (
                    <div className="space-y-4">
                      {abstractSubmissions.map((submission) => (
                        <div key={submission.abstract_id} className="space-y-4">
                          {/* Abstract Details */}
                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                              <label className="text-sm font-medium text-gray-700">Abstract Title</label>
                              <p className="mt-1 text-sm text-gray-900">{submission.title || "Untitled"}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-700">Conference Theme</label>
                              <p className="mt-1 text-sm text-gray-900">{submission.theme || "Unknown"}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-700">Publication Status</label>
                              <p className="mt-1 text-sm text-gray-900 capitalize">
                                {submission.publication_status?.replace("_", " ") || "Unknown"}
                              </p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-700">Submission Date</label>
                              <p className="mt-1 text-sm text-gray-900">
                                {submission.submit_time
                                  ? formatDate(submission.submit_time, { includeTime: true })
                                  : "Unknown"}
                              </p>
                            </div>
                          </div>

                          {/* Abstract File */}
                          <div>
                            <label className="text-sm font-medium text-gray-700">Abstract File</label>
                            <div className="mt-1 flex items-center space-x-3 rounded-lg border border-gray-200 bg-white p-3">
                              <i className="fas fa-file-word text-blue-600"></i>
                              <div className="flex-1">
                                <p className="text-sm text-gray-900">
                                  {submission.filename || getOriginalFileName(submission.abstract_path)}
                                </p>
                                <p className="text-xs text-gray-500">
                                  Submitted on {submission.submit_time ? formatDate(submission.submit_time) : "Unknown"}
                                </p>
                              </div>
                              <a
                                href={submission.abstract_path}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center space-x-1 rounded-md bg-blue-50 px-3 py-1 text-sm text-blue-700 hover:bg-blue-100"
                              >
                                <i className="fas fa-download"></i>
                                <span>Download</span>
                              </a>
                            </div>
                          </div>

                          {/* Presentation Types */}
                          <div>
                            <label className="text-sm font-medium text-gray-700">Presentation Types</label>
                            <div className="mt-1 flex gap-2">
                              {submission.oral_presentation && (
                                <Badge
                                  key="oral"
                                  variant="outline"
                                  className="border-blue-300 bg-blue-50 text-blue-700"
                                >
                                  <i className="fas fa-microphone mr-1"></i>
                                  Oral Presentation
                                </Badge>
                              )}
                              {submission.poster_presentation && (
                                <Badge
                                  key="poster"
                                  variant="outline"
                                  className="border-purple-300 bg-purple-50 text-purple-700"
                                >
                                  <i className="fas fa-image mr-1"></i>
                                  Poster Presentation
                                </Badge>
                              )}
                              {!submission.oral_presentation && !submission.poster_presentation && (
                                <span key="none" className="text-sm text-gray-900">
                                  No presentation types selected
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="pt-4">
                            <div className="flex space-x-3">
                              <Link href="/dashboard/submission">
                                <Button variant="outline">
                                  <i className="fas fa-eye mr-2"></i>
                                  View Details
                                </Button>
                              </Link>
                              {user?.emailVerified && (
                                <Link href="/dashboard/submission">
                                  <Button>
                                    <i className="fas fa-edit mr-2"></i>
                                    Edit Submission
                                  </Button>
                                </Link>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Accommodation Tab */}
          <TabsContent value="accommodation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>My Accommodation Bookings</CardTitle>
                <CardDescription>View and manage your hotel reservations</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingBookings ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                      <span className="text-sm text-gray-500">Loading your bookings...</span>
                    </div>
                  </div>
                ) : userBookings.length > 0 ? (
                  <div className="space-y-4">
                    {userBookings.map((booking, index) => (
                      <div key={booking.room_id || `booking-${index}`} className="rounded-lg border p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-medium text-gray-900">{booking.chinese_name || "Hotel Name"}</h3>
                              <Badge variant={booking.is_assigned ? "default" : "secondary"}>
                                {booking.is_assigned ? "Assigned" : "Pending Assignment"}
                              </Badge>
                            </div>
                            <p className="mt-1 text-sm text-gray-600">{booking.room_type || booking.type}</p>
                            <div className="mt-2 space-y-1 text-sm text-gray-500">
                              <div key="dates" className="flex items-center">
                                <i className="fas fa-calendar mr-2 w-4"></i>
                                <span>
                                  {formatDate(booking.checkin_date)} - {formatDate(booking.checkout_date)}
                                </span>
                              </div>
                              <div key="room" className="flex items-center">
                                <i className="fas fa-bed mr-2 w-4"></i>
                                <span>Room ID: {booking.room_id || "Not assigned"}</span>
                                {booking.roommate && booking.roommate.username && (
                                  <span className="ml-2">• Roommate: {booking.roommate.username}</span>
                                )}
                              </div>
                              <div key="price" className="flex items-center">
                                <i className="fas fa-receipt mr-2 w-4"></i>
                                <span>¥{booking.price}/night</span>
                              </div>
                              <div key="location" className="flex items-center">
                                <i className="fas fa-map-marker-alt mr-2 w-4"></i>
                                <span>{booking.location || "Location not specified"}</span>
                              </div>
                              {booking.contact_phone && (
                                <div key="contact" className="flex items-center">
                                  <i className="fas fa-phone mr-2 w-4"></i>
                                  <span>Contact: {booking.contact_phone}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    <div className="flex justify-center pt-4">
                      <Link href="/dashboard/accommodation">
                        <Button variant="outline">
                          <i className="fas fa-eye mr-2"></i>
                          View All Bookings
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <i className="fas fa-bed mb-4 text-5xl text-gray-400"></i>
                    <h3 className="mb-2 text-lg font-medium text-gray-900">No Accommodation Bookings</h3>
                    <p className="mb-6 text-gray-500">
                      You haven't made any hotel reservations yet. Book your stay for the conference.
                    </p>
                    <Link href="/dashboard/accommodation">
                      <Button>
                        <i className="fas fa-plus mr-2"></i>
                        Book Accommodation
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* 通知公告 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <UnderDevelopment
          title="Announcements System"
          description="The announcements system is currently under development. You will be able to view conference announcements once this feature is ready."
        >
          <Card className="border-border bg-card">
            <CardHeader>
              <CardTitle className="text-foreground">
                <i className="fas fa-bullhorn mr-2"></i>
                Announcements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">IFMB 2025 Registration Open</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    The International Forum on Molecular Biology (IFMB) 2025 registration is now open. Please complete
                    early bird registration by March 15, 2025, to enjoy discounted rates.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-02-01</p>
                </div>

                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">Abstract Submission Deadline Extended</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    Due to numerous requests from researchers, the abstract submission deadline has been extended to
                    July 15, 2025.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-05-20</p>
                </div>

                <div className="border-border border-l-4 py-2 pl-4">
                  <h3 className="text-foreground font-medium">New Keynote Speaker Added</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    We are pleased to announce that Nobel laureate Dr. Maria Chen will deliver a keynote speech at the
                    conference.
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">2025-06-01</p>
                </div>

                <div className="text-right">
                  <Button variant="outline">
                    <i className="fas fa-list-ul mr-2"></i>
                    View All Announcements
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </UnderDevelopment>
      </motion.div>
    </div>
  )
}
