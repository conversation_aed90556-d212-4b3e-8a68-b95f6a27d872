"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { countries } from "@/data/countries"
import { api } from "@/utils/api"
import { encryptPassword } from "@/utils/encryption"

const membershipTypes = [
  { id: 1, value: 1, label: "Student Member" },
  { id: 2, value: 2, label: "Non-Student Member" },
]

const roleTypes = [
  { id: 1, value: "Admin", label: "Admin" },
  { id: 2, value: "User", label: "User" },
]

const genders = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
]

interface User {
  id: number
  username: string
  name: string
  email: string
  gender: string
  role: number
  membership: number
  country: string
}

interface AddUserModalProps {
  onUserAdded: (user: User) => void
}

export default function AddUserModal({ onUserAdded }: AddUserModalProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [showCountryDropdown, setShowCountryDropdown] = useState(false)
  const [form, setForm] = useState({
    username: "",
    password: "",
    name: "",
    email: "",
    gender: "male",
    role: "2",
    membership: "1",
    country: "",
  })

  const [formErrors, setFormErrors] = useState({
    username: "",
    password: "",
    email: "",
    country: "",
  })

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    return emailRegex.test(email)
  }

  const validateField = (name: string, value: string) => {
    let error = ""

    switch (name) {
      case "username":
        if (value.length < 3) {
          error = "Username must be at least 3 characters"
        }
        break
      case "password":
        if (value.length < 8) {
          error = "Password must be at least 8 characters"
        }
        break
      case "email":
        if (value.length > 64) {
          error = "Email address must be at most 64 characters"
        }
        if (!validateEmail(value)) {
          error = "Please enter a valid email address"
        }
        break
      case "country":
        if (!countries.includes(value)) {
          error = "Please select a country from the list"
        }
        break
    }

    return error
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })

    // Validate field if it's one we care about
    if (["username", "password", "email", "country"].includes(name)) {
      const error = validateField(name, value)
      setFormErrors((prev) => ({ ...prev, [name]: error }))
    }
  }

  const handleCountryChange = (country: string) => {
    setForm({ ...form, country })
    setShowCountryDropdown(false)

    // Validate country
    const error = validateField("country", country)
    setFormErrors((prev) => ({ ...prev, country: error }))
  }

  useEffect(() => {
    // 添加点击事件监听器，用于关闭国家下拉框
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest("#add-user-country-dropdown-container")) {
        setShowCountryDropdown(false)
      }
    }

    if (showCountryDropdown) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    // 清理函数
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showCountryDropdown])

  const validateForm = () => {
    const newErrors = {
      username: validateField("username", form.username),
      password: validateField("password", form.password),
      email: validateField("email", form.email),
      country: validateField("country", form.country),
    }

    setFormErrors(newErrors)

    // Check if there are any errors
    return !Object.values(newErrors).some((error) => error !== "")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Form validation
    if (!validateForm()) {
      setError("Please fix the errors in the form")
      return
    }

    setLoading(true)
    setError("")

    try {
      // 对密码进行加密
      const hashedPassword = await encryptPassword(form.password)

      // 使用API工具类发送请求
      const result = await api.post<{
        code?: number
        data?: User
        msg?: string
        message?: string
      }>("/api/admin/users", {
        username: form.username,
        password: hashedPassword,
        name: form.name,
        email: form.email,
        gender: form.gender,
        role: parseInt(form.role),
        membership: parseInt(form.membership),
        country: form.country,
      })

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 0 || result.code === 200) {
          // Success
          if (result.data) {
            onUserAdded(result.data as User)
          }
          setOpen(false)
          // Reset form
          setForm({
            username: "",
            password: "",
            name: "",
            email: "",
            gender: "male",
            role: "1",
            membership: "1",
            country: "",
          })
          setFormErrors({
            username: "",
            password: "",
            email: "",
            country: "",
          })
        } else {
          setError(result.msg || result.message || "Failed to add user")
        }
      } else {
        // 处理直接返回用户数据的情况（向后兼容）
        if (result && typeof result === "object") {
          onUserAdded(result as User)
          setOpen(false)
          // Reset form
          setForm({
            username: "",
            password: "",
            name: "",
            email: "",
            gender: "male",
            role: "1",
            membership: "1",
            country: "",
          })
          setFormErrors({
            username: "",
            password: "",
            email: "",
            country: "",
          })
        } else {
          setError("Failed to add user")
        }
      }
    } catch (error) {
      console.error("Add user error:", error)
      setError("An error occurred while adding the user")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <i className="fas fa-plus mr-2"></i>
          Add User
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New User</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Basic Information</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label htmlFor="username" className="mb-1 block text-sm font-medium text-gray-700">
                  Username <span className="text-red-500">*</span>
                </label>
                <Input
                  id="username"
                  name="username"
                  placeholder="Enter username (min. 3 characters)"
                  value={form.username}
                  onChange={handleChange}
                  required
                  className={`w-full ${formErrors.username ? "border-red-500" : ""}`}
                />
                {formErrors.username && <p className="mt-1 text-xs text-red-500">{formErrors.username}</p>}
              </div>

              <div>
                <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                  Full Name <span className="text-red-500">*</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter full name"
                  value={form.name}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label htmlFor="password" className="mb-1 block text-sm font-medium text-gray-700">
                  Password <span className="text-red-500">*</span>
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter password (min. 8 characters)"
                  value={form.password}
                  onChange={handleChange}
                  required
                  className={`w-full ${formErrors.password ? "border-red-500" : ""}`}
                />
                {formErrors.password && <p className="mt-1 text-xs text-red-500">{formErrors.password}</p>}
              </div>

              <div>
                <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                  Email Address <span className="text-red-500">*</span>
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={form.email}
                  onChange={handleChange}
                  required
                  className={`w-full ${formErrors.email ? "border-red-500" : ""}`}
                />
                {formErrors.email && <p className="mt-1 text-xs text-red-500">{formErrors.email}</p>}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Additional Information</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label htmlFor="gender" className="mb-1 block text-sm font-medium text-gray-700">
                  Gender <span className="text-red-500">*</span>
                </label>
                <select
                  id="gender"
                  name="gender"
                  value={form.gender}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                  required
                >
                  {genders.map((item) => (
                    <option key={item.value} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="role" className="mb-1 block text-sm font-medium text-gray-700">
                  Role <span className="text-red-500">*</span>
                </label>
                <select
                  id="role"
                  name="role"
                  value={form.role}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                  required
                >
                  {roleTypes.map((item) => (
                    <option key={item.value} value={item.id}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="membership" className="mb-1 block text-sm font-medium text-gray-700">
                  Membership Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="membership"
                  name="membership"
                  value={form.membership}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                  required
                >
                  {membershipTypes.map((item) => (
                    <option key={item.value} value={item.id}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="country" className="mb-1 block text-sm font-medium text-gray-700">
                Country <span className="text-red-500">*</span>
              </label>
              <div className="relative" id="add-user-country-dropdown-container">
                <Input
                  id="country"
                  name="country"
                  value={form.country}
                  onChange={(e) => {
                    handleChange(e)
                    setShowCountryDropdown(true)
                  }}
                  className={`w-full ${formErrors.country ? "border-red-500" : ""}`}
                  placeholder="Type to search countries"
                  onFocus={() => setShowCountryDropdown(true)}
                  required
                />
                {formErrors.country && <p className="mt-1 text-xs text-red-500">{formErrors.country}</p>}
                {showCountryDropdown && (
                  <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-white shadow-lg">
                    {countries
                      .filter((country) => country.toLowerCase().includes((form.country || "").toLowerCase()))
                      .slice(0, 5)
                      .map((country, index) => (
                        <div
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                          onClick={() => handleCountryChange(country)}
                        >
                          {country}
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
              <span className="text-sm text-red-600">{error}</span>
            </div>
          )}

          {/* Submit button */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setOpen(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Adding User..." : "Add User"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
