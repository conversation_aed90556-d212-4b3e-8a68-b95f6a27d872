import { NextRequest, NextResponse } from "next/server"

/**
 * GET /api/admin/accommodation/reservations
 * 获取所有住宿预订信息（管理员接口）
 */
export async function GET(_request: NextRequest) {
  try {
    // 这里应该从数据库获取真实数据
    // 目前返回模拟数据，与您提供的格式完全一致
    const mockData = {
      code: 200,
      msg: "",
      data: {
        count: 4,
        list: [
          {
            RoomId: 1,
            SharedOption: 1,
            AssignedRoomId: 2,
            occupant: 28,
            checkin_date: "2025-10-16T00:00:00+08:00",
            checkout_date: "2025-10-20T00:00:00+08:00",
            is_assigned: true,
            room_id: 1,
            HotelId: 1,
            type: "1",
            price: 268,
            total: 60,
            obligate: 0,
            hotel_id: 1,
            chinese_name: "华中农业大学国际学术交流中心（IAEC）",
            location:
              "Southeast corner of Xueyuan Road & Cuizhu Road Intersection, Huazhong Agricultural University, No.1 Shizishan Street, Hongshan District, Wuhan, Hubei Province",
            contact_name: "",
            contact_phone: "+86-027-87280141",
            image_url: "/ifmb-2025-public/hotels/IAEC.jpg",
            lon: 0,
            lat: 0,
            default_checkin_date: "2025-10-16T00:00:00+08:00",
            default_checkout_date: "2025-10-20T00:00:00+08:00",
            room_type: "Standard Room",
            shared_option: "none",
            username: "550",
            gender: "male",
            country: "Taiwan(China)",
            organization: "hp",
            executor_username: "System",
            roommate: {
              executor_username: "",
              Id: 0,
              RoomId: 0,
              SharedOption: 0,
              AssignedRoomId: 0,
              occupant: 0,
              checkin_date: "0001-01-01T00:00:00Z",
              checkout_date: "0001-01-01T00:00:00Z",
              is_assigned: false,
              username: "",
              name: "",
              gender: "",
              country: "",
              organization: "",
            },
          },
          {
            RoomId: 2,
            SharedOption: 2,
            AssignedRoomId: 3,
            occupant: 29,
            checkin_date: "2025-10-16T00:00:00+08:00",
            checkout_date: "2025-10-20T00:00:00+08:00",
            is_assigned: true,
            room_id: 2,
            HotelId: 1,
            type: "2",
            price: 368,
            total: 80,
            obligate: 5,
            hotel_id: 1,
            chinese_name: "华中农业大学国际学术交流中心（IAEC）",
            location:
              "Southeast corner of Xueyuan Road & Cuizhu Road Intersection, Huazhong Agricultural University, No.1 Shizishan Street, Hongshan District, Wuhan, Hubei Province",
            contact_name: "",
            contact_phone: "+86-027-87280141",
            image_url: "/ifmb-2025-public/hotels/IAEC.jpg",
            lon: 0,
            lat: 0,
            default_checkin_date: "2025-10-16T00:00:00+08:00",
            default_checkout_date: "2025-10-20T00:00:00+08:00",
            room_type: "Deluxe Room",
            shared_option: "with partner",
            username: "551",
            gender: "female",
            country: "China",
            organization: "University A",
            executor_username: "Admin",
            roommate: {
              executor_username: "Admin",
              Id: 30,
              RoomId: 2,
              SharedOption: 2,
              AssignedRoomId: 3,
              occupant: 30,
              checkin_date: "2025-10-16T00:00:00+08:00",
              checkout_date: "2025-10-20T00:00:00+08:00",
              is_assigned: true,
              username: "552",
              name: "Jane Smith",
              gender: "female",
              country: "China",
              organization: "University A",
            },
          },
          {
            RoomId: 3,
            SharedOption: 3,
            AssignedRoomId: 4,
            occupant: 31,
            checkin_date: "2025-10-17T00:00:00+08:00",
            checkout_date: "2025-10-21T00:00:00+08:00",
            is_assigned: false,
            room_id: 3,
            HotelId: 2,
            type: "1",
            price: 200,
            total: 40,
            obligate: 2,
            hotel_id: 2,
            chinese_name: "武汉大学国际学术交流中心",
            location: "Wuhan University, Luojia Hill, Wuchang District, Wuhan, Hubei Province",
            contact_name: "Reception",
            contact_phone: "+86-027-68752000",
            image_url: "/ifmb-2025-public/hotels/WHU.jpg",
            lon: 0,
            lat: 0,
            default_checkin_date: "2025-10-16T00:00:00+08:00",
            default_checkout_date: "2025-10-20T00:00:00+08:00",
            room_type: "Standard Room",
            shared_option: "system assigned",
            username: "553",
            gender: "male",
            country: "USA",
            organization: "Research Institute B",
            executor_username: "System",
            roommate: {
              executor_username: "",
              Id: 0,
              RoomId: 0,
              SharedOption: 0,
              AssignedRoomId: 0,
              occupant: 0,
              checkin_date: "0001-01-01T00:00:00Z",
              checkout_date: "0001-01-01T00:00:00Z",
              is_assigned: false,
              username: "",
              name: "",
              gender: "",
              country: "",
              organization: "",
            },
          },
          {
            RoomId: 4,
            SharedOption: 1,
            AssignedRoomId: 5,
            occupant: 32,
            checkin_date: "2025-10-15T00:00:00+08:00",
            checkout_date: "2025-10-19T00:00:00+08:00",
            is_assigned: true,
            room_id: 4,
            HotelId: 2,
            type: "3",
            price: 500,
            total: 20,
            obligate: 1,
            hotel_id: 2,
            chinese_name: "武汉大学国际学术交流中心",
            location: "Wuhan University, Luojia Hill, Wuchang District, Wuhan, Hubei Province",
            contact_name: "Reception",
            contact_phone: "+86-027-68752000",
            image_url: "/ifmb-2025-public/hotels/WHU.jpg",
            lon: 0,
            lat: 0,
            default_checkin_date: "2025-10-16T00:00:00+08:00",
            default_checkout_date: "2025-10-20T00:00:00+08:00",
            room_type: "Suite",
            shared_option: "none",
            username: "554",
            gender: "female",
            country: "Germany",
            organization: "Max Planck Institute",
            executor_username: "Admin",
            roommate: {
              executor_username: "",
              Id: 0,
              RoomId: 0,
              SharedOption: 0,
              AssignedRoomId: 0,
              occupant: 0,
              checkin_date: "0001-01-01T00:00:00Z",
              checkout_date: "0001-01-01T00:00:00Z",
              is_assigned: false,
              username: "",
              name: "",
              gender: "",
              country: "",
              organization: "",
            },
          },
        ],
      },
    }

    return NextResponse.json(mockData)
  } catch (error) {
    console.error("Error fetching reservations:", error)
    return NextResponse.json(
      {
        code: 500,
        msg: "Internal server error",
        data: null,
      },
      { status: 500 }
    )
  }
}
